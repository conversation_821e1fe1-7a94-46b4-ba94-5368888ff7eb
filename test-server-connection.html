<!DOCTYPE html>
<html>
<head>
    <title>Test Server Connection</title>
</head>
<body>
    <h1>Testing Server Connection</h1>
    <button onclick="testServer()">Test Server</button>
    <div id="result"></div>

    <script>
        async function testServer() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:4444/api/v1/submit-email-review', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        deviceId: '507f1f77bcf86cd799439011',
                        customerId: '507f1f77bcf86cd799439013',
                        orderId: '507f1f77bcf86cd799439012',
                        rating: '5',
                        testimonial: 'Test review',
                        businessName: 'Test Restaurant'
                    })
                });
                
                if (response.ok) {
                    const html = await response.text();
                    resultDiv.innerHTML = '<h3>Success!</h3><p>Server is working. Response received.</p>';
                } else {
                    resultDiv.innerHTML = '<h3>Error</h3><p>Status: ' + response.status + '</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<h3>Connection Error</h3><p>' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
