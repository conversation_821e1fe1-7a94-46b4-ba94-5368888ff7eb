<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Star Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .star-rating {
            text-align: center;
            margin-bottom: 30px;
        }
        .star-rating input[type="radio"] {
            display: none;
        }
        .star-rating label {
            font-size: 40px;
            color: #ddd !important;
            cursor: pointer;
            margin: 0 5px;
            transition: color 0.3s ease;
            display: inline-block;
            user-select: none;
        }
        .star-rating label:hover {
            color: #ffc107 !important;
        }
        .star-rating label.active {
            color: #ffc107 !important;
        }
        .star-rating label.selected {
            color: #ffc107 !important;
        }
        .star-rating label.filled {
            color: #ffc107 !important;
        }
        .rating-text {
            text-align: center;
            margin-top: 10px;
            font-size: 18px;
            font-weight: bold;
            color: #068af5;
            min-height: 25px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #068af5; text-align: center;">Simple Star Rating Test</h1>
        
        <div class="star-rating" id="starRating">
            <input type="radio" name="rating" value="1" id="star1" required>
            <label for="star1" data-rating="1">★</label>
            <input type="radio" name="rating" value="2" id="star2">
            <label for="star2" data-rating="2">★</label>
            <input type="radio" name="rating" value="3" id="star3">
            <label for="star3" data-rating="3">★</label>
            <input type="radio" name="rating" value="4" id="star4">
            <label for="star4" data-rating="4">★</label>
            <input type="radio" name="rating" value="5" id="star5">
            <label for="star5" data-rating="5">★</label>
        </div>
        <div class="rating-text" id="ratingText">Click a star to rate</div>
        
        <div id="debug" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
            <strong>Debug Info:</strong>
            <div id="debugText">No star selected</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const starLabels = document.querySelectorAll('.star-rating label');
            const ratingText = document.getElementById('ratingText');
            const debugText = document.getElementById('debugText');
            
            const ratingTexts = {
                1: '1/5 - Poor',
                2: '2/5 - Fair', 
                3: '3/5 - Good',
                4: '4/5 - Very Good',
                5: '5/5 - Excellent'
            };
            
            // Add click event listeners to star labels
            starLabels.forEach(function(label, index) {
                label.addEventListener('click', function(e) {
                    e.preventDefault();
                    const rating = parseInt(this.getAttribute('data-rating'));
                    
                    debugText.textContent = `Clicked star ${rating}`;
                    
                    // Check the corresponding radio button
                    const radioInput = document.getElementById('star' + rating);
                    if (radioInput) {
                        radioInput.checked = true;
                    }
                    
                    // Update visual feedback immediately
                    updateStarDisplay(rating);
                    
                    // Update rating text
                    ratingText.textContent = ratingTexts[rating];
                });
            });
            
            function updateStarDisplay(rating) {
                debugText.textContent += ` | Updating display for ${rating} stars`;
                
                // Clear all stars first
                starLabels.forEach(function(label) {
                    label.classList.remove('active', 'selected', 'filled');
                    label.style.color = '#ddd';
                });
                
                // Then fill the selected stars
                for (let i = 0; i < rating; i++) {
                    const star = starLabels[i];
                    if (star) {
                        star.classList.add('active', 'selected', 'filled');
                        star.style.color = '#ffc107';
                    }
                }
            }
        });
    </script>
</body>
</html>
