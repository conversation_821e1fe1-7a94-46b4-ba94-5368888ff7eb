const express = require('express');
const cors = require('cors');
const app = express();

// Middleware
app.use(cors());
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

console.log('🚀 Starting Simple Test Server...');

// Test email review endpoint
app.post('/api/v1/submit-email-review', (req, res) => {
    console.log('📧 Email review submission received:', req.body);
    
    const { deviceId, customerId, orderId, rating, testimonial, businessName } = req.body;
    
    // Validate required fields
    if (!deviceId || !customerId || !rating) {
        console.log('❌ Missing required fields');
        return res.status(400).send(`
            <html>
            <head><title>Review Submission Error</title></head>
            <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa;">
                <div style="max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Missing required information to submit your review.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </div>
            </body>
            </html>
        `);
    }
    
    console.log('✅ Review submission successful');
    
    // Return thank you page
    const ratingTexts = {
        1: 'Poor',
        2: 'Fair',
        3: 'Good', 
        4: 'Very Good',
        5: 'Excellent'
    };
    
    const ratingValue = parseInt(rating);
    const ratingText = ratingTexts[ratingValue];
    const restaurantName = businessName || 'Test Restaurant';
    
    res.send(`
        <html>
        <head>
            <title>Thank You for Your Review!</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: 'Poppins', Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: #333;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .container {
                    max-width: 600px;
                    background: white;
                    padding: 50px;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    text-align: center;
                    animation: slideIn 0.5s ease-out;
                }
                @keyframes slideIn {
                    from { opacity: 0; transform: translateY(30px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                .success-icon {
                    font-size: 80px;
                    margin-bottom: 20px;
                    animation: bounce 1s ease-in-out;
                }
                @keyframes bounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-10px); }
                    60% { transform: translateY(-5px); }
                }
                .rating-display {
                    font-size: 28px;
                    margin: 30px 0;
                    color: #068af5;
                    font-weight: bold;
                }
                .stars {
                    font-size: 40px;
                    color: #ffc107;
                    margin: 20px 0;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .review-content {
                    background-color: #f8f9fa;
                    padding: 25px;
                    border-radius: 15px;
                    margin: 30px 0;
                    text-align: left;
                    border-left: 5px solid #068af5;
                }
                .thank-you-message {
                    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
                    padding: 30px;
                    border-radius: 15px;
                    margin: 30px 0;
                    border-left: 5px solid #28a745;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="success-icon">🎉</div>
                <h1 style="color: #068af5; margin-bottom: 15px; font-size: 36px;">Thank You!</h1>
                <p style="font-size: 20px; margin-bottom: 30px; color: #555;">Your review has been successfully submitted.</p>
                
                <div class="rating-display">
                    You rated us: ${ratingValue}/5 - ${ratingText}
                </div>
                
                <div class="stars">
                    ${'⭐'.repeat(ratingValue)}${'☆'.repeat(5 - ratingValue)}
                </div>

                ${testimonial ? `
                <div class="review-content">
                    <h3 style="margin-top: 0; color: #068af5; font-size: 20px;">Your Comment:</h3>
                    <p style="font-style: italic; margin: 0; font-size: 16px; line-height: 1.6;">"${testimonial}"</p>
                </div>
                ` : ''}

                <div class="thank-you-message">
                    <h3 style="margin-top: 0; color: #155724; font-size: 22px;">🙏 We Appreciate Your Feedback!</h3>
                    <p style="margin: 0; color: #155724; font-size: 16px; line-height: 1.6;">
                        Thank you for taking the time to review <strong>${restaurantName}</strong>!<br>
                        Your feedback helps us serve you better and improve our service.
                    </p>
                </div>

                <div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #eee; color: #666;">
                    <p style="font-size: 16px; margin-bottom: 10px;">
                        <strong>We look forward to serving you again soon!</strong>
                    </p>
                    <p style="font-size: 14px; color: #999;">
                        This is a test submission from the simple test server.
                    </p>
                </div>
            </div>
        </body>
        </html>
    `);
});

// Start server
const PORT = 4444;
app.listen(PORT, () => {
    console.log(`✅ Simple test server running on http://localhost:${PORT}`);
    console.log(`📧 Email review endpoint: http://localhost:${PORT}/api/v1/submit-email-review`);
});
