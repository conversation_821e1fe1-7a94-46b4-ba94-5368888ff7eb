// Example integrations with third-party email review services

// 1. MAILMODO INTEGRATION (Recommended)
// Mailmodo allows interactive emails with forms that work in Gmail, Apple Mail, etc.
const mailmodoIntegration = {
  // Add to your email template
  emailTemplate: `
    <!-- Mailmodo Interactive Review Form -->
    <div class="mailmodo-form" data-form-id="your-form-id">
      <h3>Rate Your Experience</h3>
      <div class="star-rating-mailmodo">
        <input type="radio" name="rating" value="5" id="star5">
        <label for="star5">⭐⭐⭐⭐⭐</label>
        <input type="radio" name="rating" value="4" id="star4">
        <label for="star4">⭐⭐⭐⭐</label>
        <!-- etc -->
      </div>
      <textarea name="comment" placeholder="Your feedback..."></textarea>
      <button type="submit">Submit Review</button>
    </div>
    
    <!-- Fallback for non-supporting clients -->
    <div class="fallback-review">
      <a href="your-review-form-url">Leave a Review</a>
    </div>
  `,
  
  // Webhook to receive submissions
  webhookHandler: async (req, res) => {
    const { rating, comment, email } = req.body;
    // Process the review submission
    // Save to your database
    res.json({ success: true });
  }
};

// 2. FERA INTEGRATION
// Fera provides embeddable review widgets and email campaigns
const feraIntegration = {
  // Install Fera script in your email template
  emailScript: `
    <script src="https://cdn.fera.ai/js/fera.js"></script>
    <div id="fera-reviews" data-product-id="your-product-id"></div>
  `,
  
  // API integration
  submitReview: async (reviewData) => {
    const response = await fetch('https://api.fera.ai/v3/reviews', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        product_id: reviewData.productId,
        customer_email: reviewData.email,
        rating: reviewData.rating,
        body: reviewData.comment
      })
    });
    return response.json();
  }
};

// 3. CUSTOMER THERMOMETER INTEGRATION
// Simple one-click rating buttons that work in all email clients
const customerThermometerIntegration = {
  // Generate rating buttons
  generateRatingButtons: (surveyId, customerId) => {
    const baseUrl = 'https://app.customerthermometer.com';
    return `
      <div style="text-align: center;">
        <p>How was your experience?</p>
        <a href="${baseUrl}/${surveyId}/5?customer=${customerId}">
          <img src="${baseUrl}/images/green.png" alt="Excellent" style="margin: 5px;">
        </a>
        <a href="${baseUrl}/${surveyId}/4?customer=${customerId}">
          <img src="${baseUrl}/images/amber.png" alt="Good" style="margin: 5px;">
        </a>
        <a href="${baseUrl}/${surveyId}/3?customer=${customerId}">
          <img src="${baseUrl}/images/orange.png" alt="OK" style="margin: 5px;">
        </a>
        <a href="${baseUrl}/${surveyId}/2?customer=${customerId}">
          <img src="${baseUrl}/images/red.png" alt="Poor" style="margin: 5px;">
        </a>
      </div>
    `;
  }
};

// 4. NICEREPLY INTEGRATION
// Professional survey and rating system
const niceReplyIntegration = {
  // Embed NiceReply survey
  emailTemplate: `
    <div style="text-align: center; margin: 20px 0;">
      <p>How satisfied are you with our service?</p>
      <a href="https://your-account.nicereply.com/survey/rate/5">
        <img src="https://cdn.nicereply.com/images/smiley-satisfied.png" alt="Very Satisfied">
      </a>
      <a href="https://your-account.nicereply.com/survey/rate/4">
        <img src="https://cdn.nicereply.com/images/smiley-happy.png" alt="Satisfied">
      </a>
      <a href="https://your-account.nicereply.com/survey/rate/3">
        <img src="https://cdn.nicereply.com/images/smiley-neutral.png" alt="Neutral">
      </a>
      <a href="https://your-account.nicereply.com/survey/rate/2">
        <img src="https://cdn.nicereply.com/images/smiley-sad.png" alt="Dissatisfied">
      </a>
      <a href="https://your-account.nicereply.com/survey/rate/1">
        <img src="https://cdn.nicereply.com/images/smiley-angry.png" alt="Very Dissatisfied">
      </a>
    </div>
  `
};

export {
  mailmodoIntegration,
  feraIntegration,
  customerThermometerIntegration,
  niceReplyIntegration
};
