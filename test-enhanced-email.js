import { generateOrderReadyEmailTemplate } from './patron-V2-server/src/templates/orderReadyEmailTemplate.js';
import fs from 'fs';

// Test the enhanced email template
const testEmailTemplate = () => {
  const customerName = "John Doe";
  const orderType = "delivery";
  const orderNo = "ORD-12345";
  const businessName = "Test Restaurant";
  const deviceInfo = { _id: "507f1f77bcf86cd799439011" };
  const apiBaseUrl = "http://localhost:4444";
  const orderId = "507f1f77bcf86cd799439012";
  const customerId = "507f1f77bcf86cd799439013";
  const userId = "507f1f77bcf86cd799439014";

  const emailHtml = generateOrderReadyEmailTemplate(
    customerName,
    orderType,
    orderNo,
    businessName,
    deviceInfo,
    apiBaseUrl,
    orderId,
    customerId,
    userId
  );

  // Save to file for testing
  fs.writeFileSync('test-enhanced-email-output.html', emailHtml);
  console.log('Enhanced email template generated successfully!');
  console.log('File saved as: test-enhanced-email-output.html');
  console.log('Contains quick rating buttons:', emailHtml.includes('Quick Rating:'));
  console.log('Contains star emojis:', emailHtml.includes('⭐⭐⭐⭐⭐'));
};

testEmailTemplate();
