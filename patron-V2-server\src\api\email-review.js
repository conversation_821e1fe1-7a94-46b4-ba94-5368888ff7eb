import device from '../models/device.js';
import customer from '../models/customer.js';
import mongoose from 'mongoose';

// Handle email review form submission
export const submitEmailReview = async (req, res) => {
    try {
        const { deviceId, customerId, orderId, rating, testimonial, businessName } = req.body;
        
        console.log('Email review submission:', {
            deviceId,
            customerId,
            orderId,
            rating,
            testimonial,
            businessName
        });

        // Validate required parameters
        if (!deviceId || !customerId || !rating) {
            return res.status(400).send(`
                <html>
                <head>
                    <title>Review Submission Error</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa; }
                        .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2 style="color: #dc3545;">❌ Error</h2>
                        <p>Missing required information to submit your review.</p>
                        <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                    </div>
                </body>
                </html>
            `);
        }

        // Validate rating value
        const ratingValue = parseInt(rating);
        if (isNaN(ratingValue) || ratingValue < 1 || ratingValue > 5) {
            return res.status(400).send(`
                <html>
                <head>
                    <title>Invalid Rating</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa; }
                        .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2 style="color: #dc3545;">❌ Invalid Rating</h2>
                        <p>Please select a rating between 1 and 5 stars.</p>
                        <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                    </div>
                </body>
                </html>
            `);
        }

        // For demo/testing purposes, allow test deviceId
        if (deviceId === '507f1f77bcf86cd799439011') {
            // This is a test submission - show demo thank you page
            const ratingTexts = {
                1: 'Poor',
                2: 'Fair',
                3: 'Good',
                4: 'Very Good',
                5: 'Excellent'
            };

            const ratingText = ratingTexts[ratingValue];
            const restaurantName = businessName || 'Test Restaurant';

            return res.send(`
                <html>
                <head>
                    <title>Thank You for Your Review!</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body {
                            font-family: 'Poppins', Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: #333;
                            min-height: 100vh;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .container {
                            max-width: 600px;
                            background: white;
                            padding: 50px;
                            border-radius: 20px;
                            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                            text-align: center;
                            animation: slideIn 0.5s ease-out;
                        }
                        @keyframes slideIn {
                            from { opacity: 0; transform: translateY(30px); }
                            to { opacity: 1; transform: translateY(0); }
                        }
                        .success-icon {
                            font-size: 80px;
                            margin-bottom: 20px;
                            animation: bounce 1s ease-in-out;
                        }
                        @keyframes bounce {
                            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                            40% { transform: translateY(-10px); }
                            60% { transform: translateY(-5px); }
                        }
                        .rating-display {
                            font-size: 28px;
                            margin: 30px 0;
                            color: #068af5;
                            font-weight: bold;
                        }
                        .stars {
                            font-size: 40px;
                            margin: 20px 0;
                            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        }
                        .review-content {
                            background-color: #f8f9fa;
                            padding: 25px;
                            border-radius: 15px;
                            margin: 30px 0;
                            text-align: left;
                            border-left: 5px solid #068af5;
                        }
                        .thank-you-message {
                            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
                            padding: 30px;
                            border-radius: 15px;
                            margin: 30px 0;
                            border-left: 5px solid #28a745;
                        }
                        .footer {
                            margin-top: 40px;
                            padding-top: 30px;
                            border-top: 2px solid #eee;
                            color: #666;
                        }
                    </style>
                    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
                </head>
                <body>
                    <div class="container">
                        <div class="success-icon">🎉</div>
                        <h1 style="color: #068af5; margin-bottom: 15px; font-size: 36px;">Thank You!</h1>
                        <p style="font-size: 20px; margin-bottom: 30px; color: #555;">Your review has been successfully submitted.</p>

                        <div class="rating-display">
                            You rated us: ${ratingValue}/5 - ${ratingText}
                        </div>

                        <div class="stars">
                            <span style="color: #ffc107;">${'⭐'.repeat(ratingValue)}</span><span style="color: #ccc;">${'☆'.repeat(5 - ratingValue)}</span>
                        </div>

                        ${testimonial ? `
                        <div class="review-content">
                            <h3 style="margin-top: 0; color: #068af5; font-size: 20px;">Your Comment:</h3>
                            <p style="font-style: italic; margin: 0; font-size: 16px; line-height: 1.6;">"${testimonial}"</p>
                        </div>
                        ` : ''}

                        <div class="thank-you-message">
                            <h3 style="margin-top: 0; color: #155724; font-size: 22px;">🙏 We Appreciate Your Feedback!</h3>
                            <p style="margin: 0; color: #155724; font-size: 16px; line-height: 1.6;">
                                Thank you for taking the time to review <strong>${restaurantName}</strong>!<br>
                                Your feedback helps us serve you better and improve our service.
                            </p>
                        </div>

                        <div class="footer">
                            <p style="font-size: 16px; margin-bottom: 10px;">
                                <strong>We look forward to serving you again soon!</strong>
                            </p>
                            <p style="font-size: 14px; color: #999;">
                                This was a demo submission. In production, this would be saved to the database.
                            </p>
                        </div>
                    </div>
                </body>
                </html>
            `);
        }

        // Validate deviceId format for real submissions
        if (!mongoose.Types.ObjectId.isValid(deviceId)) {
            return res.status(400).send(`
                <html>
                <head>
                    <title>Invalid Device</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa; }
                        .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2 style="color: #dc3545;">❌ Invalid Device</h2>
                        <p>Invalid restaurant information provided.</p>
                        <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                    </div>
                </body>
                </html>
            `);
        }

        // Find the device
        const Device = await device.findById(deviceId);
        if (!Device) {
            return res.status(404).send(`
                <html>
                <head>
                    <title>Restaurant Not Found</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa; }
                        .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2 style="color: #dc3545;">❌ Restaurant Not Found</h2>
                        <p>The restaurant information could not be found.</p>
                        <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                    </div>
                </body>
                </html>
            `);
        }

        // Get customer information
        let customerInfo = null;
        try {
            if (mongoose.Types.ObjectId.isValid(customerId)) {
                customerInfo = await customer.findById(customerId);
            }
        } catch (error) {
            console.log('Error fetching customer info:', error);
        }

        // Check for existing review from this customer for this specific order
        let existingReviewIndex = -1;
        if (Device.reviews) {
            existingReviewIndex = Device.reviews.findIndex(review =>
                review.customerId && review.customerId.toString() === customerId &&
                review.orderId && review.orderId.toString() === orderId
            );
        }

        // If review already exists for this order, show error
        if (existingReviewIndex !== -1) {
            return res.status(400).send(`
                <html>
                <head>
                    <title>Review Already Submitted</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa; }
                        .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2 style="color: #ffc107;">⚠️ Review Already Submitted</h2>
                        <p>You have already submitted a review for this order.</p>
                        <p>You can only submit one review per order.</p>
                        <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                    </div>
                </body>
                </html>
            `);
        }

        const reviewData = {
            customerId: customerId,
            customerName: customerInfo ? customerInfo.name : 'Anonymous Customer',
            rating: ratingValue,
            testimonial: testimonial || '',
            orderId: orderId || null,
            createdDate: new Date()
        };

        // Add new review
        if (!Device.reviews) {
            Device.reviews = [];
        }
        Device.reviews.push(reviewData);

        // Save the device with the new review
        await Device.save();

        // Get rating text for display
        const ratingTexts = {
            1: 'Poor',
            2: 'Fair', 
            3: 'Good',
            4: 'Very Good',
            5: 'Excellent'
        };

        const ratingText = ratingTexts[ratingValue];
        const restaurantName = businessName || Device.name || 'Restaurant';

        // Return thank you page
        res.send(`
            <html>
            <head>
                <title>Thank You for Your Review!</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: 'Poppins', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: #333;
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .container {
                        max-width: 600px;
                        background: white;
                        padding: 50px;
                        border-radius: 20px;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                        animation: slideIn 0.5s ease-out;
                    }
                    @keyframes slideIn {
                        from { opacity: 0; transform: translateY(30px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                    .success-icon {
                        font-size: 80px;
                        margin-bottom: 20px;
                        animation: bounce 1s ease-in-out;
                    }
                    @keyframes bounce {
                        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                        40% { transform: translateY(-10px); }
                        60% { transform: translateY(-5px); }
                    }
                    .rating-display {
                        font-size: 28px;
                        margin: 30px 0;
                        color: #068af5;
                        font-weight: bold;
                    }
                    .stars {
                        font-size: 40px;
                        color: #ffc107;
                        margin: 20px 0;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }
                    .review-content {
                        background-color: #f8f9fa;
                        padding: 25px;
                        border-radius: 15px;
                        margin: 30px 0;
                        text-align: left;
                        border-left: 5px solid #068af5;
                    }
                    .thank-you-message {
                        background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
                        padding: 30px;
                        border-radius: 15px;
                        margin: 30px 0;
                        border-left: 5px solid #28a745;
                    }
                    .footer {
                        margin-top: 40px;
                        padding-top: 30px;
                        border-top: 2px solid #eee;
                        color: #666;
                    }
                </style>
                <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
            </head>
            <body>
                <div class="container">
                    <div class="success-icon">🎉</div>
                    <h1 style="color: #068af5; margin-bottom: 15px; font-size: 36px;">Thank You!</h1>
                    <p style="font-size: 20px; margin-bottom: 30px; color: #555;">Your review has been successfully submitted.</p>
                    
                    <div class="rating-display">
                        You rated us: ${ratingValue}/5 - ${ratingText}
                    </div>
                    
                    <div class="stars">
                        ${'⭐'.repeat(ratingValue)}${'☆'.repeat(5 - ratingValue)}
                    </div>

                    ${testimonial ? `
                    <div class="review-content">
                        <h3 style="margin-top: 0; color: #068af5; font-size: 20px;">Your Comment:</h3>
                        <p style="font-style: italic; margin: 0; font-size: 16px; line-height: 1.6;">"${testimonial}"</p>
                    </div>
                    ` : ''}

                    <div class="thank-you-message">
                        <h3 style="margin-top: 0; color: #155724; font-size: 22px;">🙏 We Appreciate Your Feedback!</h3>
                        <p style="margin: 0; color: #155724; font-size: 16px; line-height: 1.6;">
                            Thank you for taking the time to review <strong>${restaurantName}</strong>!<br>
                            Your feedback helps us serve you better and improve our service.
                        </p>
                    </div>

                    <div class="footer">
                        <p style="font-size: 16px; margin-bottom: 10px;">
                            <strong>We look forward to serving you again soon!</strong>
                        </p>
                        <p style="font-size: 14px; color: #999;">
                            Follow us on social media for updates and special offers.
                        </p>
                    </div>
                </div>
            </body>
            </html>
        `);

    } catch (error) {
        console.error("Error in submitEmailReview:", error);
        res.status(500).send(`
            <html>
            <head><title>Review Submission Error</title></head>
            <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center; background-color: #f8f9fa;">
                <div style="max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h2 style="color: #dc3545;">❌ Server Error</h2>
                    <p>There was an error submitting your review. Please try again later.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </div>
            </body>
            </html>
        `);
    }
};
