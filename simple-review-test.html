<!DOCTYPE html>
<html>
<head>
    <title>Simple Review Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-container { max-width: 500px; margin: 0 auto; }
        .rating-option { margin: 10px 0; padding: 10px; border: 2px solid #ddd; border-radius: 5px; }
        .rating-option:hover { background-color: #f0f0f0; }
        button { background: #068af5; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Test Review Form</h2>
        
        <form onsubmit="testSubmit(event)">
            <p><strong>Select your rating:</strong></p>
            
            <div class="rating-option">
                <label>
                    <input type="radio" name="rating" value="5"> 
                    ⭐⭐⭐⭐⭐ Excellent (5 stars)
                </label>
            </div>
            
            <div class="rating-option">
                <label>
                    <input type="radio" name="rating" value="4"> 
                    ⭐⭐⭐⭐☆ Very Good (4 stars)
                </label>
            </div>
            
            <div class="rating-option">
                <label>
                    <input type="radio" name="rating" value="3"> 
                    ⭐⭐⭐☆☆ Good (3 stars)
                </label>
            </div>
            
            <div class="rating-option">
                <label>
                    <input type="radio" name="rating" value="2"> 
                    ⭐⭐☆☆☆ Fair (2 stars)
                </label>
            </div>
            
            <div class="rating-option">
                <label>
                    <input type="radio" name="rating" value="1"> 
                    ⭐☆☆☆☆ Poor (1 star)
                </label>
            </div>
            
            <p><strong>Comments (optional):</strong></p>
            <textarea name="testimonial" rows="4" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="Tell us about your experience..."></textarea>
            
            <p style="text-align: center; margin-top: 20px;">
                <button type="submit">Submit Review</button>
            </p>
        </form>
    </div>

    <script>
        function testSubmit(event) {
            event.preventDefault();
            
            const form = event.target;
            const rating = form.rating.value;
            const comment = form.testimonial.value;
            
            alert('Form submitted! Rating: ' + rating + ', Comment: ' + comment);
            
            if (!rating) {
                alert('Please select a rating!');
                return;
            }

            const ratingTexts = {
                '1': 'Poor',
                '2': 'Fair',
                '3': 'Good', 
                '4': 'Very Good',
                '5': 'Excellent'
            };

            const ratingNum = parseInt(rating);

            // Replace the entire page content
            document.body.innerHTML = `
                <div style="text-align: center; padding: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                        <div style="font-size: 60px; margin-bottom: 20px;">🎉</div>
                        <h1 style="color: #068af5; margin-bottom: 15px;">Thank You!</h1>
                        <p style="font-size: 18px; margin-bottom: 20px; color: #555;">Your review has been submitted successfully.</p>
                        
                        <div style="font-size: 24px; margin: 20px 0; color: #068af5; font-weight: bold;">
                            You rated us: ${rating}/5 - ${ratingTexts[rating]}
                        </div>
                        
                        <div style="font-size: 30px; margin: 20px 0;">
                            <span style="color: #ffc107;">${'⭐'.repeat(ratingNum)}</span><span style="color: #ccc;">${'☆'.repeat(5 - ratingNum)}</span>
                        </div>

                        ${comment ? `
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left; border-left: 4px solid #068af5;">
                            <h3 style="margin-top: 0; color: #068af5;">Your Comment:</h3>
                            <p style="font-style: italic; margin: 0;">"${comment}"</p>
                        </div>
                        ` : ''}

                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
                            <h3 style="margin-top: 0; color: #155724;">🙏 We Appreciate Your Feedback!</h3>
                            <p style="margin: 0; color: #155724;">
                                Thank you for taking the time to review us!<br>
                                Your feedback helps us improve our service.
                            </p>
                        </div>

                        <p style="margin-top: 30px; color: #666;">
                            <strong>✅ SUCCESS: Email content replaced without navigation!</strong>
                        </p>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
