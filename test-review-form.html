<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Review Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .star-rating {
            text-align: center;
            margin-bottom: 30px;
        }
        .star-rating input[type="radio"] {
            display: none;
        }
        .star-rating label {
            font-size: 40px;
            color: #ddd;
            cursor: pointer;
            margin: 0 5px;
            transition: color 0.1s ease;
            display: inline-block;
            user-select: none;
        }
        .star-rating label:hover {
            color: #ffc107;
        }
        .star-rating label.filled {
            color: #ffc107 !important;
        }
        .star-rating label.selected {
            color: #ffc107 !important;
        }
        .rating-text {
            text-align: center;
            margin-top: 10px;
            font-size: 18px;
            font-weight: bold;
            color: #068af5;
            min-height: 25px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
            box-sizing: border-box;
        }
        .submit-button {
            background-color: #068af5;
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
        }
        .submit-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Test Review Form</h1>
        <p>Click on the stars to test the rating functionality:</p>

        <form id="reviewForm">
            <div class="form-group">
                <label class="form-label">⭐ Rate Your Experience</label>
                <div class="star-rating" id="starRating">
                    <input type="radio" name="rating" value="1" id="star1" required>
                    <label for="star1" data-rating="1">★</label>
                    <input type="radio" name="rating" value="2" id="star2">
                    <label for="star2" data-rating="2">★</label>
                    <input type="radio" name="rating" value="3" id="star3">
                    <label for="star3" data-rating="3">★</label>
                    <input type="radio" name="rating" value="4" id="star4">
                    <label for="star4" data-rating="4">★</label>
                    <input type="radio" name="rating" value="5" id="star5">
                    <label for="star5" data-rating="5">★</label>
                </div>
                <div class="rating-text" id="ratingText"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="testimonial">Comments (optional)</label>
                <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your thoughts..."></textarea>
            </div>

            <button type="submit" class="submit-button">Submit Review</button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const starLabels = document.querySelectorAll('.star-rating label');
            const radioInputs = document.querySelectorAll('.star-rating input[type="radio"]');
            const ratingText = document.getElementById('ratingText');
            const starRatingContainer = document.querySelector('.star-rating');

            const ratingTexts = {
                1: '1/5 - Poor',
                2: '2/5 - Fair', 
                3: '3/5 - Good',
                4: '4/5 - Very Good',
                5: '5/5 - Excellent'
            };

            let selectedRating = 0;
            let hoverRating = 0;

            function updateStarDisplay(rating) {
                starLabels.forEach(function(label, index) {
                    const starNumber = index + 1;
                    label.classList.remove('filled', 'selected');
                    if (starNumber <= rating) {
                        label.classList.add('filled', 'selected');
                    }
                });
            }

            // Add click event listeners to star labels
            starLabels.forEach(function(label, index) {
                const starValue = parseInt(label.getAttribute('data-rating'));

                // Click event
                label.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    selectedRating = starValue;
                    
                    // Check the corresponding radio button
                    const radioInput = document.getElementById('star' + starValue);
                    if (radioInput) {
                        radioInput.checked = true;
                    }

                    // Update visual display
                    updateStarDisplay(selectedRating);
                    
                    // Update rating text
                    ratingText.textContent = ratingTexts[selectedRating];
                    
                    console.log('Selected rating:', selectedRating);
                });

                // Hover events
                label.addEventListener('mouseenter', function() {
                    if (selectedRating === 0) {
                        hoverRating = starValue;
                        starLabels.forEach(function(hoverLabel, hoverIndex) {
                            const hoverStarNumber = hoverIndex + 1;
                            hoverLabel.classList.remove('filled', 'selected');
                            if (hoverStarNumber <= hoverRating) {
                                hoverLabel.classList.add('filled');
                            }
                        });
                    }
                });
            });

            // Mouse leave event for the entire star container
            starRatingContainer.addEventListener('mouseleave', function() {
                if (selectedRating === 0) {
                    hoverRating = 0;
                    starLabels.forEach(function(label) {
                        label.classList.remove('filled', 'selected');
                    });
                }
            });

            // Form submission
            document.getElementById('reviewForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const selectedRatingInput = document.querySelector('input[name="rating"]:checked');
                if (!selectedRatingInput) {
                    alert('Please select a star rating!');
                    return false;
                }
                alert('Review submitted! Rating: ' + selectedRatingInput.value + '/5');
            });
        });
    </script>
</body>
</html>
