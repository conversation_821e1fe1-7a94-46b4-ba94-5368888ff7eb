<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email Review Submission</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .review-section {
            background-color: #f0f8ff;
            padding: 25px;
            border-radius: 8px;
            border: 2px solid #068af5;
            margin: 30px 0;
        }
        .review-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .rating-label {
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
            color: #333;
        }
        .rating-options {
            margin-bottom: 20px;
        }
        .rating-option {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: #fff;
        }
        .rating-option:hover {
            border-color: #068af5;
            background-color: #f8f9ff;
        }
        .rating-option.excellent {
            border-color: #28a745;
        }
        .rating-option.very-good {
            border-color: #17a2b8;
        }
        .rating-option.good {
            border-color: #ffc107;
        }
        .rating-option.fair {
            border-color: #fd7e14;
        }
        .rating-option.poor {
            border-color: #dc3545;
        }
        .rating-option input[type="radio"] {
            margin-right: 12px;
            transform: scale(1.2);
        }
        .rating-option input[type="radio"]:checked ~ .rating-text {
            color: #068af5;
            font-weight: bold;
        }
        .rating-option:has(input[type="radio"]:checked) {
            border-color: #068af5 !important;
            background-color: #f8f9ff;
        }
        .rating-option .stars {
            margin-right: 10px;
            font-size: 18px;
        }
        .rating-option .rating-text {
            font-weight: bold;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
            box-sizing: border-box;
        }
        .submit-button {
            background-color: #068af5;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.2s;
        }
        .submit-button:hover {
            background-color: #0056b3;
        }
        .test-info {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 5px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #068af5; text-align: center;">Test Email Review Submission</h1>
        
        <div class="test-info">
            <h3 style="margin-top: 0; color: #155724;">🧪 Test Information</h3>
            <p style="margin: 0; color: #155724;">
                This form simulates the email review submission process.<br>
                <strong>Test Device ID:</strong> 507f1f77bcf86cd799439011<br>
                <strong>Expected Result:</strong> Beautiful thank you page with your rating and comment
            </p>
        </div>

        <div class="review-section">
            <h3>📝 We Value Your Feedback!</h3>
            <p>Your thoughts help us serve you better. Please take a moment to share your experience.</p>

            <div class="review-form">
                <form id="reviewForm" action="http://localhost:4444/api/v1/submit-email-review" method="POST">
                    <input type="hidden" name="deviceId" value="507f1f77bcf86cd799439011">
                    <input type="hidden" name="customerId" value="507f1f77bcf86cd799439013">
                    <input type="hidden" name="orderId" value="507f1f77bcf86cd799439012">
                    <input type="hidden" name="businessName" value="Test Restaurant">

                    <label class="rating-label">⭐ How was your experience? Please rate us:</label>
                    <div class="rating-options">
                        <label class="rating-option excellent">
                            <input type="radio" name="rating" value="5" id="rating5" required>
                            <span class="stars">⭐⭐⭐⭐⭐</span>
                            <span class="rating-text">Excellent 5 stars</span>
                        </label>

                        <label class="rating-option very-good">
                            <input type="radio" name="rating" value="4" id="rating4">
                            <span class="stars">⭐⭐⭐⭐</span>
                            <span class="rating-text">Very Good 4 stars</span>
                        </label>

                        <label class="rating-option good">
                            <input type="radio" name="rating" value="3" id="rating3">
                            <span class="stars">⭐⭐⭐</span>
                            <span class="rating-text">Good 3 stars</span>
                        </label>

                        <label class="rating-option fair">
                            <input type="radio" name="rating" value="2" id="rating2">
                            <span class="stars">⭐⭐</span>
                            <span class="rating-text">Fair 2 stars</span>
                        </label>

                        <label class="rating-option poor">
                            <input type="radio" name="rating" value="1" id="rating1">
                            <span class="stars">⭐</span>
                            <span class="rating-text">Poor 1 star</span>
                        </label>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="testimonial">Tell us more (optional)</label>
                        <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your experience at Test Restaurant..."></textarea>
                    </div>

                    <input type="submit" class="submit-button" value="Submit Review" />
                </form>
                <p style="font-size: 13px; color: #555;"><em>Thank you for your feedback!</em></p>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background-color: #fff3cd; border-radius: 8px; border-left: 5px solid #ffc107;">
            <h4 style="margin-top: 0; color: #856404;">📋 How to Test:</h4>
            <ol style="color: #856404; margin: 0;">
                <li>Click on any rating option (like "Excellent 5 stars") to select it</li>
                <li>Notice the radio button gets selected and the border highlights</li>
                <li>Optionally add a comment in the text area</li>
                <li>Click "Submit Review" button</li>
                <li>You should be redirected to a beautiful thank you page</li>
                <li>The thank you page should show your selected rating and comment</li>
            </ol>
        </div>
    </div>

    <script>
        // Debug form submission
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('reviewForm');

            form.addEventListener('submit', function(e) {
                const formData = new FormData(form);
                console.log('Form submission data:');
                for (let [key, value] of formData.entries()) {
                    console.log(key + ': ' + value);
                }

                // Check if rating is selected
                const rating = formData.get('rating');
                if (!rating) {
                    e.preventDefault();
                    alert('Please select a rating before submitting!');
                    return false;
                }
            });
        });
    </script>
</body>
</html>
