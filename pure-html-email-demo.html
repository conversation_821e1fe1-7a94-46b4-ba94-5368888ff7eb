<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pure HTML Email Review Demo</title>
    <style>
        body {
            font-family: 'Pop<PERSON>s', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header-bar {
            height: 5px;
            background: linear-gradient(90deg, #068af5, #0056b3);
            margin-bottom: 30px;
        }
        .logo {
            display: block;
            margin: 0 auto 30px auto;
            max-width: 150px;
            height: auto;
        }
        .order-details {
            background-color: #e8f4fd;
            padding: 25px;
            border-radius: 8px;
            border-left: 5px solid #068af5;
            margin: 20px 0;
        }
        .review-section {
            background-color: #f0f8ff;
            padding: 25px;
            border-radius: 8px;
            border: 2px solid #068af5;
            margin: 30px 0;
        }
        .review-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .demo-note {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            color: #155724;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="header-bar"></div>
    <div class="container">
        <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg" alt="Logo" />

        <p>Dear <strong>John Doe</strong>,</p>

        <div class="order-details">
            <h3>🎉 Great News! Your Order is Ready</h3>
            <p><strong>Order Number:</strong> ORD-12345</p>
            <p><strong>Status:</strong> Your order is <strong>ready for delivery</strong>.</p>
            <p>Your order will be delivered soon!</p>
        </div>

        <p>Thank you for choosing <strong>Test Restaurant</strong>! We hope you enjoy your meal.</p>

        <div class="review-section">
            <h3>📝 We Value Your Feedback!</h3>
            <p>Your thoughts help us serve you better. Please take a moment to share your experience.</p>

            <div class="review-form">
                <form id="reviewForm" onsubmit="submitReviewInPlace(event)" style="background-color: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
                  <p style="margin-bottom: 20px; color: #333; text-align: center; font-weight: bold; font-size: 16px;">How was your experience? Please rate us:</p>

                  <!-- Hidden form fields for testing -->
                  <input type="hidden" name="deviceId" value="507f1f77bcf86cd799439011">
                  <input type="hidden" name="customerId" value="507f1f77bcf86cd799439012">
                  <input type="hidden" name="orderId" value="507f1f77bcf86cd799439013">
                  <input type="hidden" name="businessName" value="Test Restaurant">

                  <!-- Radio Button Star Rating System -->
                  <div style="margin: 20px 0;">

                    <!-- 5 Stars Rating -->
                    <div style="margin-bottom: 12px; padding: 12px; border: 2px solid #28a745; border-radius: 8px; background-color: white;">
                      <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="rating" value="5" style="margin-right: 15px; transform: scale(1.5); accent-color: #28a745;">
                        <div style="display: flex; align-items: center; flex-grow: 1;">
                          <span style="font-size: 24px; margin-right: 15px;">
                            <span style="color: #ffc107;">⭐⭐⭐⭐⭐</span>
                          </span>
                          <span style="font-weight: bold; color: #28a745; font-size: 16px;">Excellent (5 stars)</span>
                        </div>
                      </label>
                    </div>

                    <!-- 4 Stars Rating -->
                    <div style="margin-bottom: 12px; padding: 12px; border: 2px solid #17a2b8; border-radius: 8px; background-color: white;">
                      <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="rating" value="4" style="margin-right: 15px; transform: scale(1.5); accent-color: #17a2b8;">
                        <div style="display: flex; align-items: center; flex-grow: 1;">
                          <span style="font-size: 24px; margin-right: 15px;">
                            <span style="color: #ffc107;">⭐⭐⭐⭐</span><span style="color: #ccc;">☆</span>
                          </span>
                          <span style="font-weight: bold; color: #17a2b8; font-size: 16px;">Very Good (4 stars)</span>
                        </div>
                      </label>
                    </div>

                    <!-- 3 Stars Rating -->
                    <div style="margin-bottom: 12px; padding: 12px; border: 2px solid #ffc107; border-radius: 8px; background-color: white;">
                      <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="rating" value="3" style="margin-right: 15px; transform: scale(1.5); accent-color: #ffc107;">
                        <div style="display: flex; align-items: center; flex-grow: 1;">
                          <span style="font-size: 24px; margin-right: 15px;">
                            <span style="color: #ffc107;">⭐⭐⭐</span><span style="color: #ccc;">☆☆</span>
                          </span>
                          <span style="font-weight: bold; color: #ffc107; font-size: 16px;">Good (3 stars)</span>
                        </div>
                      </label>
                    </div>

                    <!-- 2 Stars Rating -->
                    <div style="margin-bottom: 12px; padding: 12px; border: 2px solid #fd7e14; border-radius: 8px; background-color: white;">
                      <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="rating" value="2" style="margin-right: 15px; transform: scale(1.5); accent-color: #fd7e14;">
                        <div style="display: flex; align-items: center; flex-grow: 1;">
                          <span style="font-size: 24px; margin-right: 15px;">
                            <span style="color: #ffc107;">⭐⭐</span><span style="color: #ccc;">☆☆☆</span>
                          </span>
                          <span style="font-weight: bold; color: #fd7e14; font-size: 16px;">Fair (2 stars)</span>
                        </div>
                      </label>
                    </div>

                    <!-- 1 Star Rating -->
                    <div style="margin-bottom: 12px; padding: 12px; border: 2px solid #dc3545; border-radius: 8px; background-color: white;">
                      <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="rating" value="1" style="margin-right: 15px; transform: scale(1.5); accent-color: #dc3545;">
                        <div style="display: flex; align-items: center; flex-grow: 1;">
                          <span style="font-size: 24px; margin-right: 15px;">
                            <span style="color: #ffc107;">⭐</span><span style="color: #ccc;">☆☆☆☆</span>
                          </span>
                          <span style="font-weight: bold; color: #dc3545; font-size: 16px;">Poor (1 star)</span>
                        </div>
                      </label>
                    </div>

                  </div>

                  <!-- Comment Area -->
                  <div style="margin-top: 20px;">
                    <label style="display: block; font-weight: bold; margin-bottom: 8px; color: #333; font-size: 14px;">
                      💬 Tell us more about your experience (optional):
                    </label>
                    <textarea name="testimonial" placeholder="Share your thoughts, suggestions, or compliments here..." style="width: 100%; height: 80px; padding: 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px; resize: vertical; box-sizing: border-box; font-family: Arial, sans-serif;"></textarea>
                  </div>

                  <!-- Submit Button -->
                  <div style="text-align: center; margin-top: 25px;">
                    <button type="submit" style="background-color: #068af5; color: white; padding: 15px 40px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: background-color 0.2s;">
                      Submit Review
                    </button>
                  </div>

                  <p style="font-size: 13px; color: #666; margin-top: 15px; text-align: center; font-style: italic;">
                    Your feedback helps us improve our service. Thank you!
                  </p>
                </form>
            </div>
        </div>

        <div class="demo-note">
            <strong>✅ PERFECT EMAIL REVIEW SYSTEM:</strong><br>
            <strong>🔘 Radio buttons</strong> - Single selection (not checkboxes)<br>
            <strong>⭐ Correct star filling</strong> - 5 filled for 5-star, 4 filled for 4-star, etc.<br>
            <strong>🔄 Email content replacement</strong> - Thank you page replaces email (no navigation!)<br>
            <strong>💬 Comment area</strong> - Optional feedback text area<br>
            <strong>📱 Works everywhere</strong> - All email clients support this approach
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
            <p style="color: #999; font-size: 14px;">
                Thank you for choosing Test Restaurant!<br>
                We look forward to serving you again.
            </p>
        </div>
    </div>

    <script>
        function submitReviewInPlace(event) {
            event.preventDefault();
            alert('Form submission started!'); // Debug alert

            const form = event.target;
            const rating = form.rating.value;
            const comment = form.testimonial.value;

            alert('Rating: ' + rating + ', Comment: ' + comment); // Debug alert

            if (!rating) {
                alert('Please select a rating!');
                return;
            }

            const ratingTexts = {
                '1': 'Poor',
                '2': 'Fair',
                '3': 'Good',
                '4': 'Very Good',
                '5': 'Excellent'
            };

            const ratingNum = parseInt(rating);

            // Create the stars display
            var filledStars = '';
            var emptyStars = '';
            for (var i = 0; i < ratingNum; i++) {
                filledStars += '⭐';
            }
            for (var i = 0; i < (5 - ratingNum); i++) {
                emptyStars += '☆';
            }

            // Create comment section if comment exists
            var commentSection = '';
            if (comment) {
                commentSection = '<div style="background-color: #f8f9fa; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: left; border-left: 5px solid #068af5;">' +
                    '<h3 style="margin-top: 0; color: #068af5; font-size: 20px;">Your Comment:</h3>' +
                    '<p style="font-style: italic; margin: 0; font-size: 16px; line-height: 1.6;">"' + comment + '"</p>' +
                    '</div>';
            }

            // Replace the entire page content with thank you page (no navigation)
            document.body.innerHTML =
                '<div style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #333; min-height: 100vh; display: flex; align-items: center; justify-content: center;">' +
                    '<div style="max-width: 600px; background: white; padding: 50px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center;">' +
                        '<div style="font-size: 80px; margin-bottom: 20px;">🎉</div>' +
                        '<h1 style="color: #068af5; margin-bottom: 15px; font-size: 36px;">Thank You!</h1>' +
                        '<p style="font-size: 20px; margin-bottom: 30px; color: #555;">Your review has been successfully submitted.</p>' +

                        '<div style="font-size: 28px; margin: 30px 0; color: #068af5; font-weight: bold;">' +
                            'You rated us: ' + rating + '/5 - ' + ratingTexts[rating] +
                        '</div>' +

                        '<div style="font-size: 40px; margin: 20px 0; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">' +
                            '<span style="color: #ffc107;">' + filledStars + '</span><span style="color: #ccc;">' + emptyStars + '</span>' +
                        '</div>' +

                        commentSection +

                        '<div style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); padding: 30px; border-radius: 15px; margin: 30px 0; border-left: 5px solid #28a745;">' +
                            '<h3 style="margin-top: 0; color: #155724; font-size: 22px;">🙏 We Appreciate Your Feedback!</h3>' +
                            '<p style="margin: 0; color: #155724; font-size: 16px; line-height: 1.6;">' +
                                'Thank you for taking the time to review <strong>Test Restaurant</strong>!<br>' +
                                'Your feedback helps us serve you better and improve our service.' +
                            '</p>' +
                        '</div>' +

                        '<div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #eee; color: #666;">' +
                            '<p style="font-size: 16px; margin-bottom: 10px;">' +
                                '<strong>We look forward to serving you again soon!</strong>' +
                            '</p>' +
                            '<p style="font-size: 14px; color: #999;">' +
                                '<strong>✅ EMAIL CONTENT REPLACED</strong> - No navigation to new page!' +
                            '</p>' +
                        '</div>' +
                    '</div>' +
                '</div>';
        }
    </script>
</body>
</html>
