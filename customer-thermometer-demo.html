<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Thermometer Email Demo</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header-bar {
            height: 5px;
            background: linear-gradient(90deg, #068af5, #0056b3);
            margin-bottom: 30px;
        }
        .logo {
            display: block;
            margin: 0 auto 30px auto;
            max-width: 150px;
            height: auto;
        }
        .order-details {
            background-color: #e8f4fd;
            padding: 25px;
            border-radius: 8px;
            border-left: 5px solid #068af5;
            margin: 20px 0;
        }
        .review-section {
            background-color: #f0f8ff;
            padding: 25px;
            border-radius: 8px;
            border: 2px solid #068af5;
            margin: 30px 0;
        }
        .review-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .thermometer-icons {
            text-align: center;
            margin: 20px 0;
        }
        .thermometer-icons table {
            margin: 0 auto;
            border-collapse: collapse;
        }
        .thermometer-icons td {
            padding: 10px;
            text-align: center;
        }
        .thermometer-icons img {
            width: 60px;
            height: 60px;
            border: none;
            transition: transform 0.2s;
        }
        .thermometer-icons img:hover {
            transform: scale(1.1);
        }
        .thermometer-icons span {
            display: block;
            font-size: 12px;
            font-weight: bold;
            margin-top: 5px;
        }
        .demo-note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="header-bar"></div>
    <div class="container">
        <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg" alt="Logo" />

        <p>Dear <strong>John Doe</strong>,</p>

        <div class="order-details">
            <h3>🎉 Great News! Your Order is Ready</h3>
            <p><strong>Order Number:</strong> ORD-12345</p>
            <p><strong>Status:</strong> Your order is <strong>ready for delivery</strong>.</p>
            <p>Your order will be delivered soon!</p>
        </div>

        <p>Thank you for choosing <strong>Test Restaurant</strong>! We hope you enjoy your meal.</p>

        <div class="review-section">
            <h3>📝 We Value Your Feedback!</h3>
            <p>Your thoughts help us serve you better. Please take a moment to share your experience.</p>

            <div class="review-form">
                <p style="margin-bottom: 15px; color: #333; text-align: center; font-weight: bold;">How was your experience?</p>
                
                <!-- Customer Thermometer Style Icons -->
                <div class="thermometer-icons">
                    <table>
                        <tr>
                            <td>
                                <a href="#" onclick="showRatingResult('Excellent', 5)" style="text-decoration: none;">
                                    <img src="https://app.customerthermometer.com/sites/app/images/icon_sets/icon_set_3/green.png" alt="Excellent" />
                                    <span style="color: #28a745;">Excellent</span>
                                </a>
                            </td>
                            <td>
                                <a href="#" onclick="showRatingResult('Good', 4)" style="text-decoration: none;">
                                    <img src="https://app.customerthermometer.com/sites/app/images/icon_sets/icon_set_3/amber.png" alt="Good" />
                                    <span style="color: #ffc107;">Good</span>
                                </a>
                            </td>
                            <td>
                                <a href="#" onclick="showRatingResult('OK', 3)" style="text-decoration: none;">
                                    <img src="https://app.customerthermometer.com/sites/app/images/icon_sets/icon_set_3/orange.png" alt="OK" />
                                    <span style="color: #fd7e14;">OK</span>
                                </a>
                            </td>
                            <td>
                                <a href="#" onclick="showRatingResult('Poor', 2)" style="text-decoration: none;">
                                    <img src="https://app.customerthermometer.com/sites/app/images/icon_sets/icon_set_3/red.png" alt="Poor" />
                                    <span style="color: #dc3545;">Poor</span>
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>

                <p style="font-size: 13px; color: #555; margin-top: 15px; text-align: center;"><em>Click any icon above to rate your experience - it only takes one click!</em></p>
            </div>
        </div>

        <div class="demo-note">
            <strong>🎯 DEMO:</strong> This shows how Customer Thermometer icons work in emails.<br>
            <strong>✅ Works in ALL email clients</strong> (Gmail, Outlook, Apple Mail, etc.)<br>
            <strong>📊 Industry-leading 80%+ response rates</strong><br>
            <strong>💰 Only $17/month</strong> - much cheaper than building custom solutions
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
            <p style="color: #999; font-size: 14px;">
                Thank you for choosing Test Restaurant!<br>
                We look forward to serving you again.
            </p>
        </div>
    </div>

    <script>
        function showRatingResult(rating, value) {
            alert(`🎉 Rating Submitted!\n\nYou rated: ${value}/5 - ${rating}\n\nIn the real system, this would:\n✅ Save to your database\n✅ Show a thank you page\n✅ Send notifications to your team\n✅ Track analytics`);
        }
    </script>
</body>
</html>
